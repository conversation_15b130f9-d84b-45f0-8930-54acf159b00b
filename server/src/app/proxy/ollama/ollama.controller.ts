
// import { OllamaProxyService } from "./ollama.service";

import { createElysia } from "@utils/ElysiaFactory";

// const _ollamaProxyService = new OllamaProxyService();
export const createOllamaRouter = () => {
    return createElysia({ name: "ollama", prefix: '/ollama' })

        .all("/:apikey/*", (context) => {
            return context.user;
            // return _ollamaProxyService.proxy(context);
        }, {
            auth: true
        });
};