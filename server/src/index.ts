
import { initializeElysia } from "@utils/ElysiaFactory";
import { createProxyRouter } from "@app/proxy/proxy.controller";

// Initialize MongoDB connection before starting the server
const startServer = async () => {
	try {

		// Create and start Elysia app
		const app = await initializeElysia();

		// Create proxy router after auth middleware is initialized
		const proxyRouter = createProxyRouter();

		app.get("/health", () => "OK")
			.use(proxyRouter)
			.listen(process.env.APP_PORT || 3010);

		console.log(
			`🦊 Elysia is running at ${app.server?.hostname}:${app.server?.port}`,
		);
	} catch (error) {
		console.error("Failed to start server:", error);
		process.exit(1);
	}
};

// Start the server
startServer();
