import mongoose from "mongoose";
import type { Document, Model, Types } from "mongoose";
import { Schema } from "mongoose";

// Define available actions as constants for type safety
export const ACTIONS = {
	CREATE: 'create',
	READ: 'read',
	UPDATE: 'update',
	DELETE: 'delete',
	ADMIN: 'admin'
} as const;

export type ActionType = typeof ACTIONS[keyof typeof ACTIONS];

// Define available resources
export const RESOURCES = {
	OLLAMA: 'ollama',
} as const;

export type ResourceType = typeof RESOURCES[keyof typeof RESOURCES];

export interface IPermission extends Document {
	userId: Types.ObjectId;
	resource: ResourceType;
	actions: ActionType[];
	createdAt: Date;
	updatedAt: Date;

	// Instance methods
	hasAction(action: ActionType): boolean;
	addAction(action: ActionType): Promise<IPermission>;
	removeAction(action: ActionType): Promise<IPermission>;
	hasAnyAction(actions: ActionType[]): boolean;
	hasAllActions(actions: ActionType[]): boolean;
}

// Interface for static methods
export interface IPermissionModel extends Model<IPermission> {
	findUserPermissions(userId: Types.ObjectId): Promise<IPermission[]>;
	findResourcePermissions(resource: ResourceType): Promise<IPermission[]>;
	hasUserPermission(userId: Types.ObjectId, resource: ResourceType, action: ActionType): Promise<boolean>;
	grantPermission(userId: Types.ObjectId, resource: ResourceType, actions: ActionType[]): Promise<IPermission>;
	revokePermission(userId: Types.ObjectId, resource: ResourceType, actions?: ActionType[]): Promise<boolean>;
}

const PermissionSchema = new Schema<IPermission>({
	userId: {
		type: Schema.Types.ObjectId,
		ref: 'User',
		required: true
	},
	resource: {
		type: String,
		required: true,
		enum: Object.values(RESOURCES)
	},
	actions: [{
		type: String,
		enum: Object.values(ACTIONS),
		required: true
	}]
}, {
	timestamps: true, // Automatically adds createdAt and updatedAt
	toJSON: { virtuals: true },
	toObject: { virtuals: true }
});

// Compound unique index on userId and resource
PermissionSchema.index({ userId: 1, resource: 1 }, { unique: true });

// Additional indexes for performance
PermissionSchema.index({ userId: 1 });
PermissionSchema.index({ resource: 1 });
PermissionSchema.index({ actions: 1 });

PermissionSchema.methods.hasAction = function (action: ActionType): boolean {
	return this.actions.includes(action);
};

PermissionSchema.methods.addAction = async function (action: ActionType): Promise<IPermission> {
	if (!this.actions.includes(action)) {
		this.actions.push(action);
		await this.save();
	}
	return this as unknown as IPermission;
};

PermissionSchema.methods.removeAction = async function (action: ActionType): Promise<IPermission> {
	this.actions = this.actions.filter((a: ActionType) => a !== action);
	await this.save();
	return this as unknown as IPermission;
};

PermissionSchema.methods.hasAnyAction = function (actions: ActionType[]): boolean {
	return actions.some(action => this.actions.includes(action));
};

PermissionSchema.methods.hasAllActions = function (actions: ActionType[]): boolean {
	return actions.every(action => this.actions.includes(action));
};

// Static methods
PermissionSchema.statics.findUserPermissions = function (userId: Types.ObjectId) {
	return this.find({ userId }).populate('userId', 'name email');
};

PermissionSchema.statics.findResourcePermissions = function (resource: ResourceType) {
	return this.find({ resource }).populate('userId', 'name email');
};

PermissionSchema.statics.hasUserPermission = async function (
	userId: Types.ObjectId,
	resource: ResourceType,
	action: ActionType
): Promise<boolean> {
	const permission = await this.findOne({ userId, resource });
	return permission ? permission.hasAction(action) : false;
};

PermissionSchema.statics.grantPermission = async function (
	userId: Types.ObjectId,
	resource: ResourceType,
	actions: ActionType[]
): Promise<IPermission> {
	const existingPermission = await this.findOne({ userId, resource });

	if (existingPermission) {
		// Add new actions to existing permission
		const newActions = [...new Set([...existingPermission.actions, ...actions])];
		existingPermission.actions = newActions;
		return await existingPermission.save();
	} else {
		// Create new permission
		return await this.create({ userId, resource, actions });
	}
};

PermissionSchema.statics.revokePermission = async function (
	userId: Types.ObjectId,
	resource: ResourceType,
	actions?: ActionType[]
): Promise<boolean> {
	if (!actions) {
		// Remove entire permission for resource
		const result = await this.deleteOne({ userId, resource });
		return result.deletedCount > 0;
	} else {
		// Remove specific actions
		const permission = await this.findOne({ userId, resource });
		if (permission) {
			permission.actions = permission.actions.filter(
				(action: ActionType) => !actions.includes(action)
			);

			if (permission.actions.length === 0) {
				await permission.deleteOne();
			} else {
				await permission.save();
			}
			return true;
		}
		return false;
	}
};

// Virtual for getting user info
PermissionSchema.virtual('user', {
	ref: 'User',
	localField: 'userId',
	foreignField: '_id',
	justOne: true
});

export const Permission = mongoose.model<IPermission, IPermissionModel>("Permission", PermissionSchema);
