import type { BetterAuthOptions } from 'better-auth/types'


export const PERMISSION_ACTIONS = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  ADMIN: 'admin'
} as const

export type PermissionAction = typeof PERMISSION_ACTIONS[keyof typeof PERMISSION_ACTIONS]

export const PERMISSION_RESOURCES = {
  OLLAMA: 'ollama',
} as const

export type PermissionResource = typeof PERMISSION_RESOURCES[keyof typeof PERMISSION_RESOURCES]

export interface AuthSession {
  user: {
    id: string
    email: string
    emailVerified: boolean
    name: string
    createdAt: Date
    updatedAt: Date
    image?: string | null
    banned: boolean
    role: string
    banReason?: string
    banExpires?: Date
  },
  session: {
    id: string
    userId: string
    expiresAt: Date
    createdAt: Date
    updatedAt: Date
    token: string
    ipAddress?: string
    userAgent?: string
    impersonatedBy?: string
  },
  permissions: {
    resource: PermissionResource
    actions: PermissionAction[]
  }[],
  options: BetterAuthOptions
}

export interface User {
  id: string
  email: string
  name: string
  role: 'admin' | 'user' | 'whiteListed'
  emailVerified: boolean
  createdAt: Date
  updatedAt: Date
  lastLoginAt?: Date
  // Additional profile fields
  bio?: string
  company?: string
  location?: string
  website?: string
  preferences?: {
    emailNotifications: boolean
    securityAlerts: boolean
    loginNotifications: boolean
    apiKeyAlerts: boolean
    twoFactorEnabled: boolean
  }
  permissions?: {
    resource: PermissionResource
    actions: PermissionAction[]
  }[],
}

export interface Session {
  id: string
  userId: string
  user: User
  token: string
  createdAt: Date
  expiresAt: Date

}

export interface LoginCredentials {
  email: string
  password: string
}

export interface SignupData {
  email: string
  password: string
  name: string
}

export interface AuthState {
  session: Session | null
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface AuthError {
  code: string
  message: string
  details?: Record<string, any>
}