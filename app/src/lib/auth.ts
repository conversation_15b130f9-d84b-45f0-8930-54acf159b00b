
import { createAuthClient } from 'better-auth/client'
import { adminClient, inferAdditionalFields } from 'better-auth/client/plugins'
import { createAccessControl } from 'better-auth/plugins/access';


export const statement = {
  apiKey: ["create", "read", "update", "delete", "admin"],
} as const;

const ac = createAccessControl(statement);

const whiteListed = ac.newRole({
  apiKey: ["create", "read", "update", "delete"],
})

const user = ac.newRole({
  apiKey: [],
});

const admin = ac.newRole({
  apiKey: ["create", "read", "update", "delete", "admin"],
});

export const authClient = createAuthClient({
  baseURL: import.meta.env['VITE_API_BASE_URL'] + '/auth',
  plugins: [
    adminClient({
      ac,
      adminRoles: ["admin"],
      defaultRole: "user",
      roles: {
        whiteListed,
        user,
        admin,
      },
    }),
    inferAdditionalFields({
      user: {
        resource: {
          type: "string[]",
        },
        actions: {
          type: "string[]",
        },
      }
    }),
  ]
})